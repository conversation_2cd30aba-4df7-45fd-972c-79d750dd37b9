/**
 * Constants and configuration for Technical Indicators
 * Contains default values, validation constraints, and performance settings
 */

import type { BaseIndicatorConfig, SMAConfig, RSIConfig } from '../types/indicators'

/**
 * Default configuration for all indicators
 */
export const DEFAULT_INDICATOR_CONFIG: BaseIndicatorConfig = {
  period: 14,
  enableStreaming: true,
  maxHistorySize: 1000,
  validateInputs: true
} as const

/**
 * Default configuration for Simple Moving Average
 */
export const DEFAULT_SMA_CONFIG: SMAConfig = {
  ...DEFAULT_INDICATOR_CONFIG,
  period: 20,
  priceType: 'close',
  useSmoothing: false,
  smoothingFactor: 0.1
} as const

/**
 * Default configuration for Relative Strength Index (RSI)
 */
export const DEFAULT_RSI_CONFIG: RSIConfig = {
  ...DEFAULT_INDICATOR_CONFIG,
  period: 14,
  priceType: 'close',
  smoothingMethod: 'sma',
  overboughtThreshold: 70,
  oversoldThreshold: 30
} as const

/**
 * Validation constraints for indicator configurations
 */
export const INDICATOR_VALIDATION_CONSTRAINTS = {
  period: {
    min: 1,
    max: 1000,
    default: 14
  },
  maxHistorySize: {
    min: 10,
    max: 10000,
    default: 1000
  },
  smoothingFactor: {
    min: 0.01,
    max: 1.0,
    default: 0.1
  },
  rsiThreshold: {
    min: 0,
    max: 100,
    overboughtDefault: 70,
    oversoldDefault: 30
  }
} as const

/**
 * Performance optimization settings
 */
export const INDICATOR_PERFORMANCE_CONFIG = {
  /** Maximum calculation time in milliseconds before warning */
  MAX_CALCULATION_TIME: 100,
  /** Maximum memory usage in bytes before warning */
  MAX_MEMORY_USAGE: 10 * 1024 * 1024, // 10MB
  /** Batch size for processing large datasets */
  BATCH_SIZE: 1000,
  /** Enable performance monitoring */
  ENABLE_PERFORMANCE_MONITORING: true,
  /** Enable memory optimization */
  ENABLE_MEMORY_OPTIMIZATION: true
} as const

/**
 * Error messages for indicator validation
 */
export const INDICATOR_ERROR_MESSAGES = {
  period: {
    required: 'Period is required',
    invalid: 'Period must be a positive integer',
    tooLow: `Period must be at least ${INDICATOR_VALIDATION_CONSTRAINTS.period.min}`,
    tooHigh: `Period cannot exceed ${INDICATOR_VALIDATION_CONSTRAINTS.period.max}`
  },
  data: {
    required: 'Data points are required',
    empty: 'Data array cannot be empty',
    invalid: 'Data points must be valid numbers',
    insufficientData: 'Insufficient data points for calculation',
    invalidTimestamp: 'Invalid timestamp in data point'
  },
  config: {
    invalid: 'Invalid indicator configuration',
    missingRequired: 'Missing required configuration parameters'
  },
  calculation: {
    failed: 'Indicator calculation failed',
    timeout: 'Calculation timeout exceeded',
    memoryLimit: 'Memory limit exceeded during calculation'
  }
} as const

/**
 * Supported price types for indicators
 */
export const PRICE_TYPES = {
  CLOSE: 'close',
  OPEN: 'open',
  HIGH: 'high',
  LOW: 'low',
  TYPICAL: 'typical', // (high + low + close) / 3
  WEIGHTED: 'weighted' // (high + low + 2*close) / 4
} as const

/**
 * Indicator event types
 */
export const INDICATOR_EVENTS = {
  DATA_ADDED: 'data-added',
  VALUE_CALCULATED: 'value-calculated',
  ERROR_OCCURRED: 'error-occurred',
  CONFIG_CHANGED: 'config-changed',
  RESET: 'reset',
  STREAM_STARTED: 'stream-started',
  STREAM_STOPPED: 'stream-stopped'
} as const

/**
 * Logging categories for indicators
 */
export const INDICATOR_LOG_CATEGORIES = {
  CALCULATION: 'Indicator-Calc',
  VALIDATION: 'Indicator-Valid',
  PERFORMANCE: 'Indicator-Perf',
  STREAMING: 'Indicator-Stream',
  ERROR: 'Indicator-Error'
} as const

/**
 * Default indicator names
 */
export const INDICATOR_NAMES = {
  SMA: 'SimpleMovingAverage',
  EMA: 'ExponentialMovingAverage',
  RSI: 'RelativeStrengthIndex',
  MACD: 'MACD',
  BOLLINGER_BANDS: 'BollingerBands',
  STOCHASTIC: 'Stochastic'
} as const

/**
 * Memory management settings
 */
export const MEMORY_MANAGEMENT = {
  /** Garbage collection threshold (number of operations) */
  GC_THRESHOLD: 1000,
  /** Memory cleanup interval in milliseconds */
  CLEANUP_INTERVAL: 30000,
  /** Maximum retained history per indicator */
  MAX_RETAINED_HISTORY: 5000,
  /** Enable automatic memory cleanup */
  AUTO_CLEANUP: true
} as const

/**
 * Streaming configuration
 */
export const STREAMING_CONFIG = {
  /** Default update interval for streaming in milliseconds */
  DEFAULT_UPDATE_INTERVAL: 1000,
  /** Maximum number of pending updates */
  MAX_PENDING_UPDATES: 100,
  /** Buffer size for streaming data */
  STREAM_BUFFER_SIZE: 500,
  /** Enable real-time updates */
  ENABLE_REAL_TIME: true
} as const

/**
 * Calculation optimization settings
 */
export const CALCULATION_OPTIMIZATION = {
  /** Use rolling window optimization for moving averages */
  USE_ROLLING_WINDOW: true,
  /** Enable incremental calculations */
  ENABLE_INCREMENTAL: true,
  /** Use circular buffer for memory efficiency */
  USE_CIRCULAR_BUFFER: true,
  /** Enable parallel processing for large datasets */
  ENABLE_PARALLEL_PROCESSING: false // Disabled for now due to complexity
} as const

/**
 * Validation settings
 */
export const VALIDATION_SETTINGS = {
  /** Enable strict validation mode */
  STRICT_MODE: true,
  /** Validate data types */
  VALIDATE_TYPES: true,
  /** Validate data ranges */
  VALIDATE_RANGES: true,
  /** Check for NaN and Infinity values */
  CHECK_NUMERIC_VALIDITY: true,
  /** Maximum allowed data point value */
  MAX_DATA_VALUE: Number.MAX_SAFE_INTEGER,
  /** Minimum allowed data point value */
  MIN_DATA_VALUE: Number.MIN_SAFE_INTEGER
} as const

/**
 * Export all constants as a single object for convenience
 */
export const INDICATOR_CONSTANTS = {
  DEFAULT_CONFIG: DEFAULT_INDICATOR_CONFIG,
  DEFAULT_SMA_CONFIG,
  DEFAULT_RSI_CONFIG,
  VALIDATION_CONSTRAINTS: INDICATOR_VALIDATION_CONSTRAINTS,
  PERFORMANCE_CONFIG: INDICATOR_PERFORMANCE_CONFIG,
  ERROR_MESSAGES: INDICATOR_ERROR_MESSAGES,
  PRICE_TYPES,
  EVENTS: INDICATOR_EVENTS,
  LOG_CATEGORIES: INDICATOR_LOG_CATEGORIES,
  NAMES: INDICATOR_NAMES,
  MEMORY_MANAGEMENT,
  STREAMING_CONFIG,
  CALCULATION_OPTIMIZATION,
  VALIDATION_SETTINGS
} as const
