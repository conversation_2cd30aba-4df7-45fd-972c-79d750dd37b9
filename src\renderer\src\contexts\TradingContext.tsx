/**
 * Trading Context
 * Provides shared state management for trading statistics across the application
 */

import React, { createContext, useState, useCallback, ReactNode } from 'react'
import type { TradingStatistics } from '../../../shared/types/trading'
import {
  DEFAULT_TRADING_STATISTICS,
  DEFAULT_TRADING_CONFIG
} from '../../../shared/constants/trading'
import { useBalanceWebSocketEvent } from '../hooks/useWebSocketEvents'

/**
 * Trading context value interface
 */
interface TradingContextValue {
  /** Current trading statistics */
  tradingStats: TradingStatistics
  /** Function to update trading statistics */
  setTradingStats: React.Dispatch<React.SetStateAction<TradingStatistics>>
  /** Function to reset trading statistics */
  resetTradingStats: (initialCapital?: number) => void
  /** Function to update balance */
  updateBalance: (newBalance: number) => void
}

/**
 * Trading context props interface
 */
interface TradingContextProviderProps {
  children: ReactNode
}

/**
 * Trading context
 */
const TradingContext = createContext<TradingContextValue | undefined>(undefined)

/**
 * Trading context provider component
 */
export const TradingContextProvider: React.FC<TradingContextProviderProps> = ({ children }) => {
  // Initialize trading statistics with default values
  const [tradingStats, setTradingStats] = useState<TradingStatistics>(() => ({
    ...DEFAULT_TRADING_STATISTICS,
    balance: DEFAULT_TRADING_CONFIG.tradeCapital,
    capitalPoolRemaining: DEFAULT_TRADING_CONFIG.tradeCapital
  }))

  /**
   * Resets trading statistics to initial state
   */
  const resetTradingStats = useCallback((initialCapital?: number) => {
    const capital = initialCapital || DEFAULT_TRADING_CONFIG.tradeCapital
    setTradingStats({
      ...DEFAULT_TRADING_STATISTICS,
      balance: capital,
      capitalPoolRemaining: capital
    })
  }, [])

  /**
   * Updates the balance and related statistics
   */
  const updateBalance = useCallback((newBalance: number) => {
    setTradingStats((prev) => ({
      ...prev,
      balance: newBalance
    }))
  }, [])

  /**
   * Handle balance updates from WebSocket events
   */
  const handleWebSocketBalanceUpdate = useCallback(
    (balance: number) => {
      console.log('Received balance update from WebSocket:', balance)
      updateBalance(balance)
    },
    [updateBalance]
  )

  // Set up WebSocket listener for balance updates
  useBalanceWebSocketEvent(handleWebSocketBalanceUpdate, true)

  const contextValue: TradingContextValue = {
    tradingStats,
    setTradingStats,
    resetTradingStats,
    updateBalance
  }

  return <TradingContext.Provider value={contextValue}>{children}</TradingContext.Provider>
}

export default TradingContext
