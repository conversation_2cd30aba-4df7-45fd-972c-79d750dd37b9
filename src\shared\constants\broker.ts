/**
 * Enum for connection states
 */
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

/**
 * Enum for heartbeat health status
 */
export enum HeartbeatHealth {
  HEALTHY = 'healthy',
  DEGRADED = 'degraded',
  UNHEALTHY = 'unhealthy',
  STOPPED = 'stopped'
}

/**
 * Configuration constants for connection and heartbeat management
 */
export const CONNECTION_CONFIG = {
  MAX_RECONNECT_ATTEMPTS: 5,
  RECONNECT_BASE_DELAY: 1000,
  MAX_RECONNECT_DELAY: 30000,
  PLATFORM_ID: 2,
  DEMO_MODE_FLAG: 1,
  LIVE_MODE_FLAG: 0
} as const

/**
 * Heartbeat configuration constants
 */
export const HEARTBEAT_CONFIG: HeartbeatConfig = {
  interval: 30000, // 30 seconds
  timeout: 10000, // 10 seconds
  maxMissedBeats: 3
} as const

/**
 * Socket event names
 */
export const SOCKET_EVENTS = {
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  CONNECT_ERROR: 'connect_error',
  SUCCESS_AUTH: 'successauth',
  GET_BALANCE: 'successupdateBalance',
  ASSETS: 'updateAssets',
  AUTH: 'auth',
  ERROR: 'error',
  PING: 'ps',
  PONG: 'pong'
} as const

/**
 * Broadcast event names
 */
export const BROADCAST_EVENTS = {
  CONNECTED: 'connected',
  DISCONNECTED: 'disconnected',
  CONNECTION_ERROR: 'connection_error',
  ERROR: 'error',
  ACCOUNT_BALANCE: 'balance',
  STATE_CHANGE: 'state_change',
  HEARTBEAT_SENT: 'heartbeat_sent',
  HEARTBEAT_RECEIVED: 'heartbeat_received',
  HEARTBEAT_FAILED: 'heartbeat_failed',
  HEARTBEAT_HEALTH_CHANGE: 'heartbeat_health_change'
} as const

/**
 * Auto-reconnect reasons - reasons for which automatic reconnection should be attempted
 */
export const AUTO_RECONNECT_REASONS = [
  'io server disconnect',
  'transport close',
  'transport error'
] as const
