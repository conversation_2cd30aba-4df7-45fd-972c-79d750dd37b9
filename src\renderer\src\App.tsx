import { useCallback } from 'react'
import TradingBotUI from './components/TradingBotUI'
import HeaderBalance from './components/HeaderBalance'
import { TradingContextProvider } from './contexts/TradingContext'
import type { TradingBotConfig } from '../../shared/types/trading'

function App(): React.JSX.Element {
  /**
   * Handles trading bot start
   */
  const handleTradingBotStart = useCallback((config: TradingBotConfig) => {
    console.log('Trading bot started with config:', config)
    // TODO: Integrate with PocketOption broker
    // const broker = PocketOption.getInstance(sessionId, isDemo);
    // broker.connect();
  }, [])

  /**
   * Handles trading bot stop
   */
  const handleTradingBotStop = useCallback(() => {
    console.log('Trading bot stopped')
    // TODO: Integrate with PocketOption broker
    // const broker = PocketOption.getInstance(sessionId, isDemo);
    // broker.disconnect();
  }, [])

  /**
   * Handles trading bot configuration changes
   */
  const handleConfigChange = useCallback((config: Partial<TradingBotConfig>) => {
    console.log('Trading bot config changed:', config)
  }, [])

  return (
    <TradingContextProvider>
      <div className="min-h-screen bg-gray-900 text-white">
        {/* Header */}
        <header className="bg-gray-800 border-b border-gray-700 px-6 py-4">
          <div className="max-w-6xl mx-auto flex items-center justify-between">
            {/* Left side - Title and Description */}
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-white">Kwartani Trading Bot</h1>
              <p className="text-gray-400 text-sm mt-1">
                Automated trading interface for PocketOption
              </p>
            </div>

            {/* Right side - Balance Display */}
            <HeaderBalance currencySymbol="$" ariaLabel="Current account balance display" />
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 py-8">
          <div className="max-w-6xl mx-auto px-6">
            <TradingBotUI
              onStart={handleTradingBotStart}
              onStop={handleTradingBotStop}
              onConfigChange={handleConfigChange}
            />
          </div>
        </main>

        {/* Footer */}
        <footer className="bg-gray-800 border-t border-gray-700 px-6 py-4 mt-8">
          <div className="max-w-6xl mx-auto text-center text-gray-400 text-sm">
            <p>Kwartani v7 - Trading Bot Interface</p>
            <p className="mt-1">Built with Electron, React, and TypeScript</p>
          </div>
        </footer>
      </div>
    </TradingContextProvider>
  )
}

export default App
