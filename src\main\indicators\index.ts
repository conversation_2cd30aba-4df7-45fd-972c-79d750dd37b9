/**
 * Technical Indicators Module
 * Exports all technical indicators and related utilities
 */

// Import types for internal use
import type {
  BaseIndicator,
  BaseIndicatorConfig,
  SMAConfig,
  RSIConfig,
  IndicatorDataPoint,
  IndicatorOutputPoint,
  IndicatorValidationResult,
  IndicatorFactory
} from '../../shared/types/indicators'

// Import actual classes and functions
import { SimpleMovingAverage, createSMA, calculateSMA } from './sma'
import { RelativeStrengthIndex, createRSI, calculateRSI } from './rsi'
import { INDICATOR_CONSTANTS } from '../../shared/constants/indicators'

// Base classes and interfaces
export { BaseIndicatorClass } from './BaseIndicator'

// Simple Moving Average
export {
  SimpleMovingAverage,
  createSMA,
  calculateSMA // Legacy function for backward compatibility
} from './sma'

// Relative Strength Index
export {
  RelativeStrengthIndex,
  createRSI,
  calculateRSI // Legacy function for backward compatibility
} from './rsi'

// Re-export type definitions
export type {
  BaseIndicator,
  SMAIndicator,
  RSIIndicator,
  BaseIndicatorConfig,
  SMAConfig,
  RSIConfig,
  IndicatorDataPoint,
  OHLCDataPoint,
  IndicatorOutputPoint,
  IndicatorResult,
  IndicatorMetadata,
  IndicatorValidationResult,
  IndicatorStreamUpdate,
  IndicatorEvent,
  IndicatorEventType,
  IndicatorEventCallback,
  IndicatorEventEmitter,
  IndicatorFactory,
  IndicatorRegistry,
  IndicatorPerformanceMetrics
} from '../../shared/types/indicators'

// Constants
export {
  INDICATOR_CONSTANTS,
  DEFAULT_INDICATOR_CONFIG,
  DEFAULT_SMA_CONFIG,
  DEFAULT_RSI_CONFIG,
  INDICATOR_VALIDATION_CONSTRAINTS,
  INDICATOR_PERFORMANCE_CONFIG,
  INDICATOR_ERROR_MESSAGES,
  PRICE_TYPES,
  INDICATOR_EVENTS,
  INDICATOR_LOG_CATEGORIES,
  INDICATOR_NAMES,
  MEMORY_MANAGEMENT,
  STREAMING_CONFIG,
  CALCULATION_OPTIMIZATION,
  VALIDATION_SETTINGS
} from '../../shared/constants/indicators'

/**
 * Indicator Registry Implementation
 * Manages registration and creation of indicator instances
 */
class IndicatorRegistryImpl {
  private factories = new Map<string, IndicatorFactory>()

  /**
   * Register a new indicator factory
   * @param name - Indicator name
   * @param factory - Factory function
   */
  register<T extends BaseIndicator>(name: string, factory: IndicatorFactory<T>): void {
    this.factories.set(name.toLowerCase(), factory as IndicatorFactory)
  }

  /**
   * Create an indicator instance
   * @param name - Indicator name
   * @param config - Indicator configuration
   * @returns Indicator instance
   */
  create<T extends BaseIndicator>(name: string, config: BaseIndicatorConfig): T {
    const factory = this.factories.get(name.toLowerCase())
    if (!factory) {
      throw new Error(`Indicator '${name}' is not registered`)
    }
    return factory(config) as T
  }

  /**
   * Get list of available indicator names
   * @returns Array of indicator names
   */
  getAvailableIndicators(): string[] {
    return Array.from(this.factories.keys())
  }

  /**
   * Check if an indicator is registered
   * @param name - Indicator name
   * @returns True if registered
   */
  isRegistered(name: string): boolean {
    return this.factories.has(name.toLowerCase())
  }
}

// Create and export the global indicator registry
export const indicatorRegistry = new IndicatorRegistryImpl()

// Register built-in indicators
indicatorRegistry.register('sma', (config) => new SimpleMovingAverage(config as SMAConfig))
indicatorRegistry.register(
  'simplemovingaverage',
  (config) => new SimpleMovingAverage(config as SMAConfig)
)
indicatorRegistry.register('rsi', (config) => new RelativeStrengthIndex(config as RSIConfig))
indicatorRegistry.register(
  'relativestrengthindex',
  (config) => new RelativeStrengthIndex(config as RSIConfig)
)

/**
 * Utility functions for working with indicators
 */
export const IndicatorUtils = {
  /**
   * Create an SMA indicator with default configuration
   * @param period - Period for the SMA
   * @returns SMA indicator instance
   */
  createDefaultSMA: (period: number = 20) => createSMA({ period }),

  /**
   * Create an RSI indicator with default configuration
   * @param period - Period for the RSI
   * @returns RSI indicator instance
   */
  createDefaultRSI: (period: number = 14) => createRSI({ period }),

  /**
   * Validate indicator configuration
   * @param config - Configuration to validate
   * @returns Validation result
   */
  validateConfig: (config: BaseIndicatorConfig): IndicatorValidationResult => {
    const errors: string[] = []
    const warnings: string[] = []

    if (!config.period || config.period < 1) {
      errors.push('Period must be a positive integer')
    }

    if (config.maxHistorySize && config.maxHistorySize < 10) {
      warnings.push('Max history size is very low, may affect performance')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  },

  /**
   * Convert price array to indicator data points
   * @param prices - Array of prices
   * @param startTimestamp - Starting timestamp (optional)
   * @returns Array of indicator data points
   */
  pricesToDataPoints: (prices: number[], startTimestamp?: number): IndicatorDataPoint[] => {
    const baseTime = startTimestamp || Date.now()
    return prices.map((price, index) => ({
      value: price,
      timestamp: baseTime + index * 1000 // 1 second intervals
    }))
  },

  /**
   * Extract values from indicator output points
   * @param outputPoints - Array of output points
   * @returns Array of values
   */
  extractValues: (outputPoints: IndicatorOutputPoint[]): number[] => {
    return outputPoints.map((point) => point.value)
  }
}

// Default export for convenience
export default {
  SimpleMovingAverage,
  RelativeStrengthIndex,
  createSMA,
  createRSI,
  calculateSMA,
  calculateRSI,
  indicatorRegistry,
  IndicatorUtils,
  INDICATOR_CONSTANTS
}
