/**
 * TypeScript type definitions for WebSocket Events
 * Contains all types and interfaces used by WebSocket event hooks
 */

import { ConnectionState, HeartbeatHealth } from '../../../shared/constants/broker'

/**
 * Generic WebSocket event handler function type
 * @template T - The type of data expected by the handler
 */
export type WebSocketEventHandler<T = unknown> = (data: T) => void

/**
 * WebSocket event listener configuration
 * @template T - The type of data expected by the event handler
 */
export interface WebSocketEventConfig<T = unknown> {
  /** Event name to listen for */
  eventName: string
  /** <PERSON><PERSON> function to call when event is received */
  handler: WebSocketEventHandler<T>
  /** Whether the listener is enabled (default: true) */
  enabled?: boolean
}

/**
 * Balance update event data structure
 */
export interface BalanceUpdateData {
  /** Current account balance */
  balance: number
  /** Whether this is a demo account */
  isDemo?: boolean
}

/**
 * Connection state change event data structure
 */
export interface ConnectionStateChangeData {
  /** Previous connection state */
  from: ConnectionState
  /** New connection state */
  to: ConnectionState
  /** Timestamp when the change occurred */
  timestamp: number
}

/**
 * Heartbeat event data structure
 */
export interface HeartbeatEventData {
  /** Timestamp of the heartbeat event */
  timestamp: number
  /** Number of missed heartbeats */
  missedBeats: number
  /** Response time in milliseconds (for received events) */
  responseTime?: number
}

/**
 * Heartbeat health change event data structure
 */
export interface HeartbeatHealthChangeData {
  /** Previous health status */
  from: HeartbeatHealth
  /** New health status */
  to: HeartbeatHealth
  /** Number of missed heartbeats */
  missedBeats: number
}

/**
 * Heartbeat failed event data structure
 */
export interface HeartbeatFailedData {
  /** Number of missed heartbeats */
  missedBeats: number
  /** Maximum allowed missed heartbeats */
  maxMissedBeats: number
}

/**
 * Error event data structure
 */
export interface ErrorEventData {
  /** Error message */
  error: string
  /** Error code (optional) */
  code?: string | number
  /** Additional error details (optional) */
  details?: Record<string, unknown>
}

/**
 * Disconnect event data structure
 */
export interface DisconnectEventData {
  /** Reason for disconnection */
  reason: string
  /** Whether reconnection will be attempted */
  willReconnect?: boolean
}

/**
 * Generic broker event data that can contain any of the above types
 */
export type BrokerEventData = 
  | BalanceUpdateData
  | ConnectionStateChangeData
  | HeartbeatEventData
  | HeartbeatHealthChangeData
  | HeartbeatFailedData
  | ErrorEventData
  | DisconnectEventData
  | unknown

/**
 * Type guard to check if data is a valid balance update
 */
export function isBalanceUpdateData(data: unknown): data is BalanceUpdateData {
  return typeof data === 'object' && 
         data !== null && 
         'balance' in data && 
         typeof (data as BalanceUpdateData).balance === 'number' &&
         Number.isFinite((data as BalanceUpdateData).balance)
}

/**
 * Type guard to check if data is a valid connection state change
 */
export function isConnectionStateChangeData(data: unknown): data is ConnectionStateChangeData {
  return typeof data === 'object' && 
         data !== null && 
         'from' in data && 
         'to' in data && 
         'timestamp' in data &&
         typeof (data as ConnectionStateChangeData).from === 'string' &&
         typeof (data as ConnectionStateChangeData).to === 'string' &&
         typeof (data as ConnectionStateChangeData).timestamp === 'number'
}

/**
 * Type guard to check if data is a valid heartbeat event
 */
export function isHeartbeatEventData(data: unknown): data is HeartbeatEventData {
  return typeof data === 'object' && 
         data !== null && 
         'timestamp' in data && 
         'missedBeats' in data &&
         typeof (data as HeartbeatEventData).timestamp === 'number' &&
         typeof (data as HeartbeatEventData).missedBeats === 'number'
}

/**
 * Type guard to check if data is a valid error event
 */
export function isErrorEventData(data: unknown): data is ErrorEventData {
  return typeof data === 'object' && 
         data !== null && 
         'error' in data &&
         typeof (data as ErrorEventData).error === 'string'
}

/**
 * Type guard to check if data is a valid disconnect event
 */
export function isDisconnectEventData(data: unknown): data is DisconnectEventData {
  return typeof data === 'object' && 
         data !== null && 
         'reason' in data &&
         typeof (data as DisconnectEventData).reason === 'string'
}
